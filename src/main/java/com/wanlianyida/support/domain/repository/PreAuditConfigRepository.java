package com.wanlianyida.support.domain.repository;

import com.wanlianyida.support.domain.model.entity.PreAuditConfigEntity;

import java.util.List;

/**
 * <AUTHOR>
 * @Description 预审配置仓储接口
 * @Date 2025年08月01日
 */
public interface PreAuditConfigRepository {

    /**
     * 插入预审配置
     */
    boolean insert(PreAuditConfigEntity entity);

    /**
     * 根据ID更新预审配置
     */
    boolean updateById(PreAuditConfigEntity entity);

    /**
     * 根据ID删除预审配置
     */
    boolean deleteById(Long id);

    /**
     * 根据ID查询预审配置
     */
    PreAuditConfigEntity queryById(Long id);

    /**
     * 根据线路ID查询预审配置
     */
    List<PreAuditConfigEntity> queryByLineId(String lineId);

    /**
     * 根据企业ID查询预审配置
     */
    List<PreAuditConfigEntity> queryByCompanyId(String companyId);

    /**
     * 根据线路ID和企业ID查询预审配置
     */
    PreAuditConfigEntity queryByLineIdAndCompanyId(String lineId, String companyId);

    /**
     * 条件查询预审配置列表
     */
    List<PreAuditConfigEntity> queryByCondition(PreAuditConfigEntity condition);
}
