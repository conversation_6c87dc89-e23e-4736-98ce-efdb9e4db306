package com.wanlianyida.support.domain.model.entity;

import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 预审配置实体
 * @Date 2025年08月01日
 */
@Data
@ApiModel("预审配置")
public class PreAuditConfigEntity {

    private Long id;

    private String lineId;

    private String lineName;

    private String companyId;

    private String companyName;

    private Integer preAuditType;

    private String creatorId;

    private String creatorName;

    private Date createTime;

    private String lastUpdaterId;

    private String lastUpdaterName;

    private Date lastUpdateTime;
}
