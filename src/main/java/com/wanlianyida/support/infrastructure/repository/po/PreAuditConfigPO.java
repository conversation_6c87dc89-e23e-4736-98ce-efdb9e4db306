package com.wanlianyida.support.infrastructure.repository.po;

import com.baomidou.mybatisplus.annotation.IdType;
import com.baomidou.mybatisplus.annotation.TableId;
import com.baomidou.mybatisplus.annotation.TableName;
import com.baomidou.mybatisplus.extension.activerecord.Model;
import io.swagger.annotations.ApiModel;
import lombok.Data;

import java.util.Date;

/**
 * <AUTHOR>
 * @Description 预审配置持久化对象
 * @Date 2025年08月01日
 */
@Data
@ApiModel("预审配置")
@TableName("plf_pre_audit_config")
public class PreAuditConfigPO extends Model<PreAuditConfigPO> {

    @TableId(value = "id", type = IdType.AUTO)
    private Long id;

    private String lineId;

    private String lineName;

    private String companyId;

    private String companyName;

    private Integer preAuditType;

    private String creatorId;

    private String creatorName;

    private Date createTime;

    private String lastUpdaterId;

    private String lastUpdaterName;

    private Date lastUpdateTime;
}
